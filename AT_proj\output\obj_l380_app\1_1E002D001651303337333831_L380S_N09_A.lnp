--cpu=Cortex-M4.fp.sp
".\output\obj_l380_app\code_convert.o"
".\output\obj_l380_app\error.o"
".\output\obj_l380_app\my_sd_read.o"
".\output\obj_l380_app\wruid.o"
".\output\obj_l380_app\usart3.o"
".\output\obj_l380_app\sw_udp.o"
".\output\obj_l380_app\one_key.o"
".\output\obj_l380_app\ledcontrol.o"
".\output\obj_l380_app\switch_csrs.o"
".\output\obj_l380_app\sn_top.o"
".\output\obj_l380_app\error_frame_handler.o"
".\output\obj_l380_app\sw_sn_boot.o"
".\output\obj_l380_app\sw_ff.o"
".\output\obj_l380_app\sn_cmd.o"
".\output\obj_l380_app\delay.o"
".\output\obj_l380_app\sys.o"
".\output\obj_l380_app\usart.o"
".\output\obj_l380_app\my_random.o"
".\output\obj_l380_app\sw_debug.o"
".\output\obj_l380_app\led.o"
".\output\obj_l380_app\timer.o"
".\output\obj_l380_app\lan8720.o"
".\output\obj_l380_app\sdmmc_sdcard.o"
".\output\obj_l380_app\iwdg.o"
".\output\obj_l380_app\w25q80.o"
".\output\obj_l380_app\my_timeout_timer.o"
".\output\obj_l380_app\malloc.o"
".\output\obj_l380_app\lwip_comm.o"
".\output\obj_l380_app\fs.o"
".\output\obj_l380_app\etharp.o"
".\output\obj_l380_app\ethernetif.o"
".\output\obj_l380_app\autoip.o"
".\output\obj_l380_app\icmp.o"
".\output\obj_l380_app\igmp.o"
".\output\obj_l380_app\inet.o"
".\output\obj_l380_app\inet_chksum.o"
".\output\obj_l380_app\ip.o"
".\output\obj_l380_app\ip_addr.o"
".\output\obj_l380_app\ip_frag.o"
".\output\obj_l380_app\def.o"
".\output\obj_l380_app\dhcp.o"
".\output\obj_l380_app\dns.o"
".\output\obj_l380_app\init.o"
".\output\obj_l380_app\lwip_sys.o"
".\output\obj_l380_app\mem.o"
".\output\obj_l380_app\memp.o"
".\output\obj_l380_app\netif.o"
".\output\obj_l380_app\pbuf.o"
".\output\obj_l380_app\raw.o"
".\output\obj_l380_app\stats.o"
".\output\obj_l380_app\tcp.o"
".\output\obj_l380_app\tcp_in.o"
".\output\obj_l380_app\tcp_out.o"
".\output\obj_l380_app\timers.o"
".\output\obj_l380_app\udp.o"
".\output\obj_l380_app\sys_arch.o"
".\output\obj_l380_app\api_lib.o"
".\output\obj_l380_app\api_msg.o"
".\output\obj_l380_app\err.o"
".\output\obj_l380_app\netbuf.o"
".\output\obj_l380_app\netdb.o"
".\output\obj_l380_app\netifapi.o"
".\output\obj_l380_app\sockets.o"
".\output\obj_l380_app\tcpip.o"
".\output\obj_l380_app\diskio.o"
".\output\obj_l380_app\ff.o"
".\output\obj_l380_app\ffsystem.o"
".\output\obj_l380_app\ffunicode.o"
".\output\obj_l380_app\exfuns.o"
".\output\obj_l380_app\artnet.o"
".\output\obj_l380_app\config.o"
".\output\obj_l380_app\controller_play.o"
".\output\obj_l380_app\controller_porperty.o"
".\output\obj_l380_app\led_driver_bsp.o"
".\output\obj_l380_app\one_key_address.o"
".\output\obj_l380_app\transposition.o"
".\output\obj_l380_app\udp_use.o"
".\output\obj_l380_app\uart.o"
".\output\obj_l380_app\en_delay.o"
".\output\obj_l380_app\f_geth_driver.o"
".\output\obj_l380_app\flash.o"
".\output\obj_l380_app\ip_set.o"
".\output\obj_l380_app\led_addressing.o"
".\output\obj_l380_app\led_param_config.o"
".\output\obj_l380_app\led_type.o"
".\output\obj_l380_app\ledboard_ountputdriver.o"
".\output\obj_l380_app\sm16813.o"
".\output\obj_l380_app\iwdg_1.o"
".\output\obj_l380_app\backup _sram.o"
".\output\obj_l380_app\en_top.o"
".\output\obj_l380_app\croutine.o"
".\output\obj_l380_app\event_groups.o"
".\output\obj_l380_app\list.o"
".\output\obj_l380_app\queue.o"
".\output\obj_l380_app\tasks.o"
".\output\obj_l380_app\timers_1.o"
".\output\obj_l380_app\stream_buffer.o"
".\output\obj_l380_app\heap_4.o"
".\output\obj_l380_app\port.o"
".\output\obj_l380_app\main.o"
".\output\obj_l380_app\ftpd_event.o"
"..\swlib\AT32F4Lib_1E002D001651303337333831.lib"
".\output\obj_l380_app\ftpd.o"
".\output\obj_l380_app\display.o"
".\output\obj_l380_app\font_lib.o"
".\output\obj_l380_app\key.o"
".\output\obj_l380_app\key_app.o"
".\output\obj_l380_app\key_event.o"
".\output\obj_l380_app\lcd_driver.o"
".\output\obj_l380_app\ui_app.o"
".\output\obj_l380_app\ui_display.o"
".\output\obj_l380_app\digital_display.o"
".\output\obj_l380_app\addr_check_app.o"
".\output\obj_l380_app\addr_check_data.o"
".\output\obj_l380_app\brightness_data.o"
".\output\obj_l380_app\cover_work_sta.o"
".\output\obj_l380_app\cover_top.o"
".\output\obj_l380_app\dmx_app.o"
".\output\obj_l380_app\dmx_data.o"
".\output\obj_l380_app\dmx_hal.o"
".\output\obj_l380_app\dmx_service.o"
".\output\obj_l380_app\frame_app.o"
".\output\obj_l380_app\frame_data.o"
".\output\obj_l380_app\random_effect_app.o"
".\output\obj_l380_app\master_slave_app.o"
".\output\obj_l380_app\master_slave_data.o"
".\output\obj_l380_app\play_base_param_data.o"
".\output\obj_l380_app\random_sys.o"
".\output\obj_l380_app\ip_data.o"
".\output\obj_l380_app\auto_id_app.o"
".\output\obj_l380_app\save_param_app.o"
".\output\obj_l380_app\save_param_hal.o"
".\output\obj_l380_app\save_param_service.o"
".\output\obj_l380_app\artnet_data.o"
".\output\obj_l380_app\prj_lock_app.o"
".\output\obj_l380_app\ic_type.o"
".\output\obj_l380_app\gamma_data.o"
".\output\obj_l380_app\error_code_app.o"
".\output\obj_l380_app\at32f435_437_int.o"
".\output\obj_l380_app\at32f435_437_clock.o"
".\output\obj_l380_app\at32f435_437_acc.o"
".\output\obj_l380_app\at32f435_437_adc.o"
".\output\obj_l380_app\at32f435_437_can.o"
".\output\obj_l380_app\at32f435_437_crc.o"
".\output\obj_l380_app\at32f435_437_crm.o"
".\output\obj_l380_app\at32f435_437_dac.o"
".\output\obj_l380_app\at32f435_437_debug.o"
".\output\obj_l380_app\at32f435_437_dma.o"
".\output\obj_l380_app\at32f435_437_dvp.o"
".\output\obj_l380_app\at32f435_437_edma.o"
".\output\obj_l380_app\at32f435_437_emac.o"
".\output\obj_l380_app\at32f435_437_ertc.o"
".\output\obj_l380_app\at32f435_437_exint.o"
".\output\obj_l380_app\at32f435_437_flash.o"
".\output\obj_l380_app\at32f435_437_gpio.o"
".\output\obj_l380_app\at32f435_437_i2c.o"
".\output\obj_l380_app\at32f435_437_misc.o"
".\output\obj_l380_app\at32f435_437_pwc.o"
".\output\obj_l380_app\at32f435_437_qspi.o"
".\output\obj_l380_app\at32f435_437_scfg.o"
".\output\obj_l380_app\at32f435_437_sdio.o"
".\output\obj_l380_app\at32f435_437_spi.o"
".\output\obj_l380_app\at32f435_437_tmr.o"
".\output\obj_l380_app\at32f435_437_usart.o"
".\output\obj_l380_app\at32f435_437_usb.o"
".\output\obj_l380_app\at32f435_437_wdt.o"
".\output\obj_l380_app\at32f435_437_wwdt.o"
".\output\obj_l380_app\at32f435_437_xmc.o"
".\output\obj_l380_app\system_at32f435_437.o"
".\output\obj_l380_app\startup_at32f435_437_ext_ram.o"
".\libraries\cmsis\dsp\Lib_ARM\arm_cortexM4lf_math.lib"
".\output\obj_l380_app\at32_emac.o"
".\output\obj_l380_app\at32_myflash.o"
".\output\obj_l380_app\ch443k_hal.o"
".\output\obj_l380_app\ch443k_service.o"
".\output\obj_l380_app\fft_app.o"
".\output\obj_l380_app\fft_data.o"
".\output\obj_l380_app\mcp4017t_hal.o"
".\output\obj_l380_app\mcp4017t_service.o"
".\output\obj_l380_app\music_voice_control_hal.o"
".\output\obj_l380_app\music_voice_control_service.o"
".\output\obj_l380_app\externalcontrol_app.o"
".\output\obj_l380_app\externalcontrol_data.o"
".\output\obj_l380_app\externalcontrol_hal.o"
".\output\obj_l380_app\externalcontrol_service.o"
".\output\obj_l380_app\iir_stm32.o"
".\output\obj_l380_app\pid_c_stm32.o"
".\output\obj_l380_app\cr4_fft_64_stm32.o"
".\output\obj_l380_app\cr4_fft_256_stm32.o"
".\output\obj_l380_app\cr4_fft_1024_stm32.o"
".\output\obj_l380_app\fir_stm32.o"
".\output\obj_l380_app\iirarma_stm32.o"
".\output\obj_l380_app\pid_stm32.o"
".\output\obj_l380_app\bluetooth_app.o"
".\output\obj_l380_app\bluetooth_data.o"
".\output\obj_l380_app\bluetooth_service.o"
".\output\obj_l380_app\bluetooth_hal.o"
--strict --scatter ".\output\obj_l380_app\1_1E002D001651303337333831_L380S_N09_A.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\output\listings\1_1E002D001651303337333831_L380S_N09_A.map" -o .\output\obj_l380_app\1_1E002D001651303337333831_L380S_N09_A.axf