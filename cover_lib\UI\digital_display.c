#include "digital_display.h"


// 数码管显示编码：0-9数字对应的段码
// bit0-bit6分别对应A-G段
uint16_t digital_display_value[DIG_DISPLAY_NUM] = {
    0x3F,  // 0: 0b0111111 - 点亮ABCDEF
    0x06,  // 1: 0b0000110 - 点亮BC
    0x5B,  // 2: 0b1011011 - 点亮ABDEG
    0x4F,  // 3: 0b1001111 - 点亮ABCDG
    0x66,  // 4: 0b1100110 - 点亮BCFG
    0x6D,  // 5: 0b1101101 - 点亮ACDFG
    0x7D,  // 6: 0b1111101 - 点亮ACDEFG
    0x07,  // 7: 0b0000111 - 点亮ABC
    0x7F,  // 8: 0b1111111 - 点亮ABCDEFG
    0x6F   // 9: 0b1101111 - 点亮ABCDFG
};

// 十位数数码管
uint16_t DIG1_GPIO_PIN[DIG_GPIO_NUM] = {DIG1_PIN_0, DIG1_PIN_1, DIG1_PIN_2, DIG1_PIN_3, DIG1_PIN_4, DIG1_PIN_5, DIG1_PIN_6};
// 个位数数码管
uint16_t DIG2_GPIO_PIN[DIG_GPIO_NUM] = {DIG2_PIN_0, DIG2_PIN_1, DIG2_PIN_2, DIG2_PIN_3, DIG2_PIN_4, DIG2_PIN_5, DIG2_PIN_6};

// DIG_PIN_0 对应 A
// DIG_PIN_1 对应 B
// DIG_PIN_2 对应 C
// DIG_PIN_3 对应 D
// DIG_PIN_4 对应 E
// DIG_PIN_5 对应 F
// DIG_PIN_6 对应 G

// gpio init
void DigitalDisplay_GPIO_Init(void)
{
    gpio_init_type gpio_init_struct;

    /* enable the gpioa clock */
    crm_periph_clock_enable(CRM_GPIOB_PERIPH_CLOCK, TRUE);
    crm_periph_clock_enable(CRM_GPIOC_PERIPH_CLOCK, TRUE);

    /* set default parameter */
    gpio_default_para_init(&gpio_init_struct);

    /* configure the gpio */
    gpio_init_struct.gpio_drive_strength = GPIO_DRIVE_STRENGTH_STRONGER;
    gpio_init_struct.gpio_out_type       = GPIO_OUTPUT_OPEN_DRAIN; // 开漏输出
    gpio_init_struct.gpio_mode           = GPIO_MODE_OUTPUT;
    // gpio_init_struct.gpio_pins           = GPIO_PIN_3 | GPIO_PIN_4 | GPIO_PIN_5 | GPIO_PIN_6 | GPIO_PIN_7 | GPIO_PIN_8 | GPIO_PIN_9 | GPIO_PIN_10;
    gpio_init_struct.gpio_pins           = GPIO_PIN_3 | GPIO_PIN_4 | GPIO_PIN_5 | GPIO_PIN_6 | GPIO_PIN_7 | GPIO_PIN_8 | GPIO_PIN_9;
    gpio_init_struct.gpio_pull           = GPIO_PULL_NONE;
    gpio_init(GPIOB, &gpio_init_struct);

    gpio_init_struct.gpio_pins           = GPIO_PIN_6 | GPIO_PIN_7 | GPIO_PIN_8 | GPIO_PIN_9 | GPIO_PIN_10 | GPIO_PIN_11 | GPIO_PIN_12;
    gpio_init(GPIOC, &gpio_init_struct);

    gpio_init_struct.gpio_pins = GPIO_PIN_10;
    gpio_init_struct.gpio_mode = GPIO_MODE_INPUT;
    gpio_init_struct.gpio_pull = GPIO_PULL_UP; // 上拉输入 // 关闭数码管
    gpio_init(GPIOB, &gpio_init_struct);
}

//TODO: 
void Display_ID(void)
{
    uint8_t id = GetId();
    DigitalDisplay_Show(id);
}

// 数码管显示
void DigitalDisplay_Show(uint8_t value)
{
    uint16_t display_code_tens, display_code_units;
    uint8_t tens, units;

    // 确保输入值在0-99范围内
    if (value > 99)
        value = 99;

    // 分解十位和个位
    tens = value / 10;
    units = value % 10;

    // 获取对应数字的显示编码
    display_code_tens = digital_display_value[tens];
    display_code_units = digital_display_value[units];

    // 控制十位数码管 (DIG1)
    for (uint8_t i = 0; i < DIG_GPIO_NUM; i++)
    {
        // 检查该位是否需要点亮(1表示点亮)
        if (display_code_tens & (1 << i))
        {
            // 点亮对应段
            gpio_bits_set(GPIOB, DIG1_GPIO_PIN[i]);  // 高电平点亮
        }
        else
        {
            // 熄灭对应段
            gpio_bits_reset(GPIOB, DIG1_GPIO_PIN[i]);    // 低电平熄灭
        }
    }

    // 控制个位数码管 (DIG2)
    for (uint8_t i = 0; i < DIG_GPIO_NUM; i++)
    {
        // 检查该位是否需要点亮(1表示点亮)
        if (display_code_units & (1 << i))
        {
            // 点亮对应段
            gpio_bits_set(GPIOC, DIG2_GPIO_PIN[i]);  // 高电平点亮
        }
        else
        {
            // 熄灭对应段
            gpio_bits_reset(GPIOC, DIG2_GPIO_PIN[i]);    // 低电平熄灭
        }
    }
}
