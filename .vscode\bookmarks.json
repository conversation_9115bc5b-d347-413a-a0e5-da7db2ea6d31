{"files": [{"path": "en_lib/src/LedBoard_OuntputDriver.c", "bookmarks": [{"line": 8, "column": 10, "label": ""}, {"line": 10, "column": 18, "label": ""}, {"line": 290, "column": 9, "label": ""}, {"line": 483, "column": 9, "label": ""}, {"line": 484, "column": 15, "label": ""}, {"line": 846, "column": 10, "label": ""}, {"line": 847, "column": 14, "label": ""}, {"line": 1491, "column": 18, "label": ""}]}, {"path": "en_lib/inc/LED_Addressing.h", "bookmarks": [{"line": 44, "column": 63, "label": ""}]}, {"path": "en_lib/src/transposition.c", "bookmarks": [{"line": 3548, "column": 21, "label": ""}, {"line": 3550, "column": 20, "label": ""}, {"line": 3658, "column": 32, "label": ""}, {"line": 3665, "column": 16, "label": ""}]}, {"path": "en_lib/src/led_driver_bsp.c", "bookmarks": [{"line": 52, "column": 11, "label": ""}, {"line": 53, "column": 16, "label": ""}, {"line": 92, "column": 34, "label": ""}]}, {"path": "SYSTEM/usart/usart.h", "bookmarks": [{"line": 44, "column": 17, "label": ""}, {"line": 45, "column": 17, "label": ""}]}, {"path": "en_lib/inc/LED_Data_Def.h", "bookmarks": [{"line": 44, "column": 11, "label": ""}, {"line": 45, "column": 15, "label": ""}]}, {"path": "en_lib/src/LED_Type.c", "bookmarks": [{"line": 2565, "column": 17, "label": ""}]}, {"path": "USER/en_top.c", "bookmarks": [{"line": 654, "column": 39, "label": ""}]}, {"path": "cover_lib/save/save_param_app.c", "bookmarks": [{"line": 664, "column": 26, "label": ""}]}, {"path": "en_lib/src/flash.c", "bookmarks": [{"line": 620, "column": 16, "label": ""}, {"line": 622, "column": 24, "label": ""}]}, {"path": "cover_lib/UI/digital_display.c", "bookmarks": [{"line": 62, "column": 13, "label": ""}]}, {"path": "en_lib/src/udp_use.c", "bookmarks": [{"line": 689, "column": 17, "label": ""}, {"line": 690, "column": 34, "label": ""}, {"line": 3395, "column": 11, "label": ""}, {"line": 3396, "column": 12, "label": ""}]}, {"path": "en_lib/src/controller_play.c", "bookmarks": [{"line": 52, "column": 11, "label": ""}, {"line": 53, "column": 15, "label": ""}]}]}