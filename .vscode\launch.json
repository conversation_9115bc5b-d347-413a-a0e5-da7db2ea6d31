{"version": "0.2.0", "configurations": [{"name": "C/C++ Runner: Debug Session", "type": "cppdbg", "request": "launch", "args": [], "stopAtEntry": false, "externalConsole": true, "cwd": "c:/JBin/202409/02AN380CTL/APP/APP/AN380/VSum_AN380_CTLV1.2.2_202504101646/en_lib/src", "program": "c:/JBin/202409/02AN380CTL/APP/APP/AN380/VSum_AN380_CTLV1.2.2_202504101646/en_lib/src/build/Debug/outDebug", "MIMode": "gdb", "miDebuggerPath": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}]}]}