Stack trace:
Frame         Function      Args
0007FFFF8E80  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF7D80) msys-2.0.dll+0x2118E
0007FFFF8E80  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFF9158) msys-2.0.dll+0x69BA
0007FFFF8E80  0002100469F2 (00021028DF99, 0007FFFF8D38, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF8E80  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF8E80  00021006A545 (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFF9160  00021006B9A5 (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE394C0000 ntdll.dll
7FFE38EC0000 KERNEL32.DLL
7FFE36A60000 KERNELBASE.dll
7FFE37DC0000 USER32.dll
7FFE36E50000 win32u.dll
000210040000 msys-2.0.dll
7FFE373D0000 GDI32.dll
7FFE36FD0000 gdi32full.dll
7FFE369B0000 msvcp_win.dll
7FFE36E80000 ucrtbase.dll
7FFE38F90000 advapi32.dll
7FFE39050000 msvcrt.dll
7FFE37920000 sechost.dll
7FFE38050000 RPCRT4.dll
7FFE35B80000 CRYPTBASE.DLL
7FFE36610000 bcryptPrimitives.dll
7FFE392F0000 IMM32.DLL
