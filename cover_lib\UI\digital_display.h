#include "main.h"

// GPIO 说明
// PB3~PB10 对应 DIG1的0~7引脚 8个引脚
// PC6~PC12 对应 DIG2的0~6引脚 7个引脚

// 0~9 10个数字
#define DIG_DISPLAY_NUM 10
// 数码管引脚数量
#define DIG_GPIO_NUM 7

// 时钟
#define DIG1_PORT GPIOB
#define DIG1_PIN_0 GPIO_PIN_3
#define DIG1_PIN_1 GPIO_PIN_4
#define DIG1_PIN_2 GPIO_PIN_5
#define DIG1_PIN_3 GPIO_PIN_6
#define DIG1_PIN_4 GPIO_PIN_7
#define DIG1_PIN_5 GPIO_PIN_8
#define DIG1_PIN_6 GPIO_PIN_9
#define DIG1_PIN_7 GPIO_PIN_10

#define DIG2_PORT GPIOC
#define DIG2_PIN_0 GPIO_PIN_6
#define DIG2_PIN_1 GPIO_PIN_7
#define DIG2_PIN_2 GPIO_PIN_8
#define DIG2_PIN_3 GPIO_PIN_9
#define DIG2_PIN_4 GPIO_PIN_10
#define DIG2_PIN_5 GPIO_PIN_11
#define DIG2_PIN_6 GPIO_PIN_12

// 函数声明
void DigitalDisplay_GPIO_Init(void);
void Display_ID(void);
void DigitalDisplay_Show(uint8_t value);

