{"C_Cpp.errorSquiggles": "Disabled", "files.associations": {"config.h": "c", "cover_work_sta.h": "c", "ui_app.h": "c", "ui_common.h": "c", "brightness_data.h": "c", "dmx_data.h": "c", "dmx_hal.h": "c", "key.h": "c", "sys.h": "c", "led.h": "c", "master_slave_app.h": "c", "master_slave_typedef.h": "c", "dmx_service.h": "c", "frame_data.h": "c", "cover_top.h": "c", "limits": "c", "memory": "c", "prj_lock_app.h": "c", "save_param_typedef.h": "c", "artnet_data.h": "c", "master_slave_data.h": "c", "type_def_sys.h": "c", "ic_type.h": "c", "save_param_app.h": "c", "ip_data.h": "c", "strvar.h": "c", "string": "c", "gamma_data.h": "c", "sn_cmd.h": "c", "usart3.h": "c", "sw_udp.h": "c", "my_sd_read.h": "c", "error_code_app.h": "c", "sn_top.h": "c", "sw_sn_boot.h": "c", "frame_app.h": "c", "auto_id_app.h": "c", "play_base_param_data.h": "c", "play_base_param_type_def.h": "c", "key_app.h": "c", "dmx_app.h": "c", "error.h": "c", "display.h": "c", "ledcontrol.h": "c", "font_lib.h": "c", "stm32h7xx.h": "c", "lcd_driver.h": "c", "sdmmc_sdcard.h": "c", "random_sys.h": "c", "key_event.h": "c", "ui_display.h": "c", "main.h": "c", "sw_ff.h": "c", "Backup _SRAM.C": "cpp", "f_geth_driver.h": "c", "stm32h7xx_hal.h": "c", "delay.h": "c", "en_delay.h": "c", "error_frame_handler.h": "c", "controller_property.h": "c", "ledboard_ountputdriver.h": "c", "save_param_hal.h": "c", "at32_myflash.h": "c", "my_flash.h": "c", "sm16813.h": "c", "udp_use.h": "c", "one_key_address.h": "c", "iwdg.h": "c", "malloc.h": "c", "array": "c", "string_view": "c", "system_error": "c", "sw_debug.h": "c", "compare": "c", "new": "c", "led_param_config.h": "c", "SM16813.H": "cpp", "SM16813.C": "cpp"}, "clangd.fallbackFlags": ["-std=c++2a", "-I${workspaceFolder}", "-I${workspaceFolder}\\AT_proj\\libraries\\cmsis\\cm4\\core_support", "-I${workspaceFolder}\\AT_proj\\libraries\\cmsis\\cm4\\device_support", "-I${workspaceFolder}\\AT_proj\\libraries\\cmsis\\dsp\\ComputeLibrary\\Include", "-I${workspaceFolder}\\AT_proj\\libraries\\cmsis\\dsp\\include", "-I${workspaceFolder}\\AT_proj\\libraries\\cmsis\\dsp\\PrivateInclude", "-I${workspaceFolder}\\AT_proj\\libraries\\cmsis\\dsp\\Source\\DistanceFunctions", "-I${workspaceFolder}\\AT_proj\\libraries\\drivers\\inc", "-I${workspaceFolder}\\AT_proj\\user\\inc", "-I${workspaceFolder}\\chip_param_message", "-I${workspaceFolder}\\chip_param_message\\GS", "-I${workspaceFolder}\\chip_param_message\\Others", "-I${workspaceFolder}\\chip_param_message\\SM", "-I${workspaceFolder}\\CORE", "-I${workspaceFolder}\\cover_lib\\addr_check", "-I${workspaceFolder}\\cover_lib\\artnet_data", "-I${workspaceFolder}\\cover_lib\\audio_ctrl", "-I${workspaceFolder}\\cover_lib\\auto_id", "-I${workspaceFolder}\\cover_lib\\ble_ctrl", "-I${workspaceFolder}\\cover_lib\\brightness", "-I${workspaceFolder}\\cover_lib\\cover_top", "-I${workspaceFolder}\\cover_lib\\dmx", "-I${workspaceFolder}\\cover_lib\\error_code", "-I${workspaceFolder}\\cover_lib\\ext_ctrl", "-I${workspaceFolder}\\cover_lib\\frame", "-I${workspaceFolder}\\cover_lib\\gamma", "-I${workspaceFolder}\\cover_lib\\ic_type", "-I${workspaceFolder}\\cover_lib\\ip_setting", "-I${workspaceFolder}\\cover_lib\\master_slave", "-I${workspaceFolder}\\cover_lib\\play_base_param", "-I${workspaceFolder}\\cover_lib\\prj_lock", "-I${workspaceFolder}\\cover_lib\\save", "-I${workspaceFolder}\\cover_lib\\sys", "-I${workspaceFolder}\\cover_lib\\sys\\def", "-I${workspaceFolder}\\cover_lib\\sys\\random", "-I${workspaceFolder}\\cover_lib\\UI", "-I${workspaceFolder}\\en_lib\\decBin_API_MCU", "-I${workspaceFolder}\\en_lib\\inc", "-I${workspaceFolder}\\FATFS\\exfuns", "-I${workspaceFolder}\\FATFS\\source", "-I${workspaceFolder}\\FreeRTOS\\include", "-I${workspaceFolder}\\FreeRTOS\\portable\\GCC\\ARM_CM0", "-I${workspaceFolder}\\FreeRTOS\\portable\\GCC\\ARM_CM3", "-I${workspaceFolder}\\FreeRTOS\\portable\\GCC\\ARM_CM3_MPU", "-I${workspaceFolder}\\FreeRTOS\\portable\\GCC\\ARM_CM4_MPU", "-I${workspaceFolder}\\FreeRTOS\\portable\\GCC\\ARM_CM4F", "-I${workspaceFolder}\\FreeRTOS\\portable\\GCC\\ARM_CM7\\r0p1", "-I${workspaceFolder}\\FreeRTOS\\portable\\GCC\\ARM_CM7_MPU\\r0p1", "-I${workspaceFolder}\\FreeRTOS\\portable\\IAR\\ARM_CM0", "-I${workspaceFolder}\\FreeRTOS\\portable\\IAR\\ARM_CM3", "-I${workspaceFolder}\\FreeRTOS\\portable\\IAR\\ARM_CM4_MPU", "-I${workspaceFolder}\\FreeRTOS\\portable\\IAR\\ARM_CM4F", "-I${workspaceFolder}\\FreeRTOS\\portable\\IAR\\ARM_CM7\\r0p1", "-I${workspaceFolder}\\FreeRTOS\\portable\\IAR\\ARM_CM7_MPU\\r0p1", "-I${workspaceFolder}\\FreeRTOS\\portable\\RVDS\\ARM_CM0", "-I${workspaceFolder}\\FreeRTOS\\portable\\RVDS\\ARM_CM3", "-I${workspaceFolder}\\FreeRTOS\\portable\\RVDS\\ARM_CM4_MPU", "-I${workspaceFolder}\\FreeRTOS\\portable\\RVDS\\ARM_CM4F", "-I${workspaceFolder}\\FreeRTOS\\portable\\RVDS\\ARM_CM7\\r0p1", "-I${workspaceFolder}\\FreeRTOS\\portable\\RVDS\\ARM_CM7_MPU\\r0p1", "-I${workspaceFolder}\\FreeRTOS\\portable\\Tasking\\ARM_CM4F", "-I${workspaceFolder}\\HALLIB\\STM32H7xx_HAL_Driver\\Inc", "-I${workspaceFolder}\\HALLIB\\STM32H7xx_HAL_Driver\\Inc\\Legacy", "-I${workspaceFolder}\\HARDWARE\\24CXX", "-I${workspaceFolder}\\HARDWARE\\ADC", "-I${workspaceFolder}\\HARDWARE\\AP3216C", "-I${workspaceFolder}\\HARDWARE\\DCMI", "-I${workspaceFolder}\\HARDWARE\\DHT11", "-I${workspaceFolder}\\HARDWARE\\DS18B20", "-I${workspaceFolder}\\HARDWARE\\ES8388", "-I${workspaceFolder}\\HARDWARE\\IIC", "-I${workspaceFolder}\\HARDWARE\\IWDG", "-I${workspaceFolder}\\HARDWARE\\JPEGCODEC", "-I${workspaceFolder}\\HARDWARE\\KEY", "-I${workspaceFolder}\\HARDWARE\\LAN8720", "-I${workspaceFolder}\\HARDWARE\\LCD", "-I${workspaceFolder}\\HARDWARE\\LED", "-I${workspaceFolder}\\HARDWARE\\MPU", "-I${workspaceFolder}\\HARDWARE\\NAND", "-I${workspaceFolder}\\HARDWARE\\NRF24L01", "-I${workspaceFolder}\\HARDWARE\\OV5640", "-I${workspaceFolder}\\HARDWARE\\PCF8574", "-I${workspaceFolder}\\HARDWARE\\RTC", "-I${workspaceFolder}\\HARDWARE\\SAI", "-I${workspaceFolder}\\HARDWARE\\SDMMC", "-I${workspaceFolder}\\HARDWARE\\SDRAM", "-I${workspaceFolder}\\HARDWARE\\SPI", "-I${workspaceFolder}\\HARDWARE\\TIMER", "-I${workspaceFolder}\\HARDWARE\\USART2", "-I${workspaceFolder}\\HARDWARE\\W25QXX", "-I${workspaceFolder}\\HARDWARE\\W25QXX\\not_used", "-I${workspaceFolder}\\lwip-ftpd", "-I${workspaceFolder}\\lwip-ftpd\\test-in-docker\\lwip-include", "-I${workspaceFolder}\\lwip-ftpd\\test-in-docker\\lwip-include\\arch", "-I${workspaceFolder}\\LWIP\\arch", "-I${workspaceFolder}\\LWIP\\lwip-1.4.1\\src\\include\\ipv4\\lwip", "-I${workspaceFolder}\\LWIP\\lwip-1.4.1\\src\\include\\ipv6\\lwip", "-I${workspaceFolder}\\LWIP\\lwip-1.4.1\\src\\include\\lwip", "-I${workspaceFolder}\\LWIP\\lwip-1.4.1\\src\\include\\netif", "-I${workspaceFolder}\\LWIP\\lwip-1.4.1\\src\\include\\posix", "-I${workspaceFolder}\\LWIP\\lwip-1.4.1\\src\\include\\posix\\sys", "-I${workspaceFolder}\\LWIP\\lwip-1.4.1\\src\\netif\\ppp", "-I${workspaceFolder}\\LWIP\\lwip-1.4.1\\test\\unit", "-I${workspaceFolder}\\LWIP\\lwip-1.4.1\\test\\unit\\core", "-I${workspaceFolder}\\LWIP\\lwip-1.4.1\\test\\unit\\etharp", "-I${workspaceFolder}\\LWIP\\lwip-1.4.1\\test\\unit\\tcp", "-I${workspaceFolder}\\LWIP\\lwip-1.4.1\\test\\unit\\udp", "-I${workspaceFolder}\\LWIP\\lwip_app\\lwip_comm", "-I${workspaceFolder}\\LWIP\\lwip_app\\sw", "-I${workspaceFolder}\\LWIP\\lwip_app\\tcp_client_demo", "-I${workspaceFolder}\\LWIP\\lwip_app\\tcp_server_demo", "-I${workspaceFolder}\\LWIP\\lwip_app\\udp_demo", "-I${workspaceFolder}\\LWIP\\lwip_app\\web_server_demo", "-I${workspaceFolder}\\MALLOC", "-I${workspaceFolder}\\mcu", "-I${workspaceFolder}\\PICTURE", "-I${workspaceFolder}\\STM32F10x_DSP_Lib\\inc", "-I${workspaceFolder}\\SYSTEM\\delay", "-I${workspaceFolder}\\SYSTEM\\random", "-I${workspaceFolder}\\SYSTEM\\sys", "-I${workspaceFolder}\\SYSTEM\\usart", "-I${workspaceFolder}\\TEXT", "-I${workspaceFolder}\\USER", "-I${workspaceFolder}\\USMART"], "C_Cpp.default.defines": ["EXTEND_SRAM=FLASH_EOPB0_SRAM_512K", "AT32F43x", "AT32F437VGT7", "USE_STDPERIPH_DRIVER", "AT_START_F437_V1", "MERGED_TO_SN", "MERGED_WITH_EN", "ERX_TASK", "SD_APP_ENABLED", "AN380", "AN3", "EN508W", "CHECK_NETWORK", "USING_BOOT", "LED_PACKET_CNT_TEST", "AN380_C2", "SW_DEBUG_UDP_PRINT", "LEDDATA_NO_LWIP", "SEEKWAY_VER", "LOW_COST", "L382_V1", "SW_DEBUG", "LEDOutput_64CHs"], "C_Cpp.intelliSenseEngineFallback": "enabled", "C_Cpp.clang_format_path": "D:\\Program Files\\LLVM\\bin\\clang-format.exe", "C_Cpp.dimInactiveRegions": true, "C_Cpp_Runner.cCompilerPath": "gcc", "C_Cpp_Runner.cppCompilerPath": "g++", "C_Cpp_Runner.debuggerPath": "gdb", "C_Cpp_Runner.cStandard": "", "C_Cpp_Runner.cppStandard": "", "C_Cpp_Runner.msvcBatchPath": "C:/Program Files/Microsoft Visual Studio/VR_NR/Community/VC/Auxiliary/Build/vcvarsall.bat", "C_Cpp_Runner.useMsvc": false, "C_Cpp_Runner.warnings": ["-Wall", "-Wextra", "-Wpedantic", "-W<PERSON>dow", "-Wformat=2", "-Wcast-align", "-Wconversion", "-Wsign-conversion", "-W<PERSON><PERSON>-dereference"], "C_Cpp_Runner.msvcWarnings": ["/W4", "/permissive-", "/w14242", "/w14287", "/w14296", "/w14311", "/w14826", "/w44062", "/w44242", "/w14905", "/w14906", "/w14263", "/w44265", "/w14928"], "C_Cpp_Runner.enableWarnings": true, "C_Cpp_Runner.warningsAsError": false, "C_Cpp_Runner.compilerArgs": [], "C_Cpp_Runner.linkerArgs": [], "C_Cpp_Runner.includePaths": [], "C_Cpp_Runner.includeSearch": ["*", "**/*"], "C_Cpp_Runner.excludeSearch": ["**/build", "**/build/**", "**/.*", "**/.*/**", "**/.vscode", "**/.vscode/**"], "C_Cpp_Runner.useAddressSanitizer": false, "C_Cpp_Runner.useUndefinedSanitizer": false, "C_Cpp_Runner.useLeakSanitizer": false, "C_Cpp_Runner.showCompilationTime": false, "C_Cpp_Runner.useLinkTimeOptimization": false, "C_Cpp_Runner.msvcSecureNoWarnings": false}